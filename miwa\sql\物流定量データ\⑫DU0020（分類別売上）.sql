SELECT
SUBSTR(DU20.U<PERSON>_YMD,0,6) URI_YM
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
,DU20.URI_HEN_KBN
,SUM(DU20.URI_NUKI_GAKU) URI_NUKI_GAKU
FROM
DU0020 DU20
LEFT JOIN MS0090 MS90
ON MS90.SHIIRE_CD = DU20.SHIIRE_CD
AND MS90.SHOHIN_CD = DU20.SHOHIN_CD
LEFT JOIN MS0020 MS20_SIHABU
ON MS20_SIHABU.SHIKIBETU_CD = 'SIHABU'
AND MS20_SIHABU.REC_KBN = '2'
AND MS20_SIHABU.CD = MS90.KAMOKU_BUNRUI
LEFT JOIN MS0020 MS20_BUMBUN
ON MS20_BUMBUN.SHIKIBETU_CD = 'BUMBUN'
AND MS20_BUMBUN.REC_KBN = '2'
AND MS20_BUMBUN.CD = MS90.BUMON_BUNRUI
WHERE
( DU20.URI_YMD LIKE :TARGET_YM ||'%'
OR DU20.URI_YMD LIKE :LAST_YEAR_YM ||'%'
OR DU20.URI_YMD LIKE :LAST_MONTH_YM || '%'
OR DU20.URI_YMD LIKE :LAST_BOTH_YM || '%'
)
AND DU20.LAST_DEN_FLG = '1'
AND DU20.LINE_CD < 60
GROUP BY
SUBSTR(DU20.URI_YMD,0,6)
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
,DU20.URI_HEN_KBN
ORDER BY
SUBSTR(DU20.URI_YMD,0,6)
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
,DU20.URI_HEN_KBN