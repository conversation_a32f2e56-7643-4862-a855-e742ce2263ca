INSERT INTO MIWA_T910
(
BUT_CENTER_KBN
,BUT_CENTER
,SHII<PERSON>_CD
,<PERSON><PERSON><PERSON>_CD
,BARA_SU
,TOROK<PERSON>_YMD
,TOROKU_TIME
,TOROKU_CD
,TOROKU_PG
,K<PERSON>H<PERSON>_YMD
,KOS<PERSON><PERSON>_TIME
,K<PERSON>HIN_CD
,KOS<PERSON><PERSON>_PG
,KOSHIN_KAISU
)
SELECT
DJ20.SHUKKA_BUT_CENTER_KBN BUT_CENTER_KBN
,DJ20.SHUKKA_BUT_CENTER BUT_CENTER
,DJ20.SHIIRE_CD SHIIRE_CD
,DJ20.SHOHIN_CD SHOHIN_CD
,SUM(DJ20.TEISEI_BARA_SU) BARA_SU
,:TOROKU_YMD TOROKU_YMD
,:TOROKU_TIME TOROKU_TIME
,:TOROKU_CD TOROKU_CD
,:TOROKU_PG TOROKU_PG
,:K<PERSON><PERSON><PERSON>_<PERSON><PERSON> KOSHIN_YMD
,:<PERSON><PERSON><PERSON><PERSON>_TIME <PERSON>IN_TIME
,:<PERSON><PERSON><PERSON>IN_CD KOSHIN_CD
,:<PERSON><PERSON><PERSON>IN_PG KOSHIN_PG
,:<PERSON><PERSON><PERSON><PERSON>_KAISU KOSHIN_KAISU
FROM
MIYATA_KIKAN.DJ0020 DJ20
WHERE 1=1
AND DJ20.SHUKKA_BUT_CENTER_KBN = '2'
AND JYUCHU_YMD BETWEEN :DJFROM AND :TODAY
GROUP BY
DJ20.SHUKKA_BUT_CENTER_KBN
,DJ20.SHUKKA_BUT_CENTER
,DJ20.SHIIRE_CD
,DJ20.SHOHIN_CD