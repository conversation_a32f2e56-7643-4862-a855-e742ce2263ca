-- DZ0390 その他在庫データのスナップ作成
delete from DZ0390_SNAP where SNAP_YMD = (select SHORI_YMD from MS0010)
;
insert into DZ0390_SNAP
(
SNAP_YMD ,SNAP_YM ,BUT_CENTER_KBN ,BUT_CENTER ,ZAIKO_KBN
,SH<PERSON>RE_CD ,SHOHIN_CD ,SHI_SHO_CD ,SHOMI_KIGEN_YMD ,ZAIKO_SU
,TOROKU_YMD ,TOROKU_TIME ,TOROKU_CD ,TOROKU_PG
,KOSHIN_YMD ,KOSHIN_TIME ,KOSHIN_CD ,KOSHIN_PG
,KOSHIN_KAISU
)
select
A.SHORI_YMD ,A.<PERSON>_<PERSON> ,B.BUT_CENTER_KBN ,B.BUT_CENTER ,B.ZAIKO_KBN
,B.SHIIRE_CD ,B.SHOHIN_CD ,B.SHI_SHO_CD ,B.SHOMI_KIGEN_YMD ,B.<PERSON><PERSON><PERSON><PERSON>_SU
,<PERSON><PERSON>_<PERSON> ,<PERSON>.<PERSON>RO<PERSON>U_TIME ,<PERSON><PERSON>_<PERSON> ,<PERSON><PERSON>RO<PERSON>U_PG
,<PERSON><PERSON>_<PERSON> ,<PERSON><PERSON>IN_TIME ,B.KOSHIN_CD ,B.KOSHIN_PG
,B.KOSHIN_KAISU
from
MS0010 A
,DZ0390 B
;
commit
;
exit
;