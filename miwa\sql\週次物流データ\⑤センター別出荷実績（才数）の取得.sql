select
CAL.CD as 識別CD
,CAL.NAME as 対象期間
,BUT.BUT_CENTER as 物流センタCD
,SUM(
case DU70.CLS_ORD_UNIT
when 'A' then MS90.AW_SAISU * (du70.NUM_SHP / (MS90.IRISU1 * MS90.IRISU2) )
when 'C' then MS90.CS_SAISU * (du70.NUM_SHP / (MS90.IRISU1 * MS90.IRISU2) )
when 'B' then MS90.BL_SAISU * (du70.NUM_SHP / (MS90.IRISU1) )
when 'P' then MS90.PS_SAISU * (du70.NUM_SHP)
else 0
end
) as 出荷数量
from (
select 1 as CD, '今週' as NAME, :WEEK_FROM as FROMDATE, :WEEK_TO as TODATE from dual union all
select 2 as CD, '前年同週' as NAME, :LAST_YEAR_WEEK_FROM as FROMDATE, :LAST_YEAR_WEEK_TO as TODATE from dual union all
select 3 as CD, '前週' as NAME, :LAST_WEEK_FROM as FROMDATE, :LAST_WEEK_TO as TODATE from dual
) CAL
cross join MS0290 BUT
left join DU0070 DU70
on DU70.YMD_RCV between CAL.FROMDATE and CAL.TODATE
and BUT.BUT_CENTER_KBN = '2'
and BUT.BUT_CENTER = DU70.CD_CNT
left join MS0090 MS90
on DU70.CD_ITEM = MS90.SHI_SHO_CD
where
BUT.BUT_CENTER_KBN = '2'
group by
CAL.CD
,CAL.NAME
,BUT.BUT_CENTER
order by
CAL.CD
,CAL.NAME
,BUT.BUT_CENTER