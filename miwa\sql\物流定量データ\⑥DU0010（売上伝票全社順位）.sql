SELECT
SUBSTR(DU10.U<PERSON>_YMD,0,6) URI_YM
,DU10.TOK<PERSON>1_CD
,SUM(DU10.URI_NUKI_GAKU) URI_NUKI_GAKU
,MS70.TOKUI_RYA
FROM
DU0010 DU10
LEFT JOIN MS0070 MS70
ON MS70.TOKUI1_KEY = DU10.TOKUI1_KEY
AND MS70.TOKUI2_CD = '0000'
AND MS70.TOKUI3_CD = '0000'
WHERE
(DU10.URI_YMD LIKE :TARGET_YM ||'%' OR DU10.URI_YMD LIKE :LAST_YEAR_YM ||'%')
AND DU10.LAST_DEN_FLG = '1'
AND DU10.LINE_CD < 60
GROUP BY
SUBSTR(DU10.URI_YMD,0,6)
,DU10.TOKUI1_CD
,MS70.TOKUI_RYA
ORDER BY
SUBSTR(DU10.U<PERSON>_YMD,0,6)
,SUM(DU10.URI_NUKI_GAKU) DESC