SELECT
SUBSTR(DU10.U<PERSON>_YMD,0,6) URI_YM
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,SUM(DU10.URI_NUKI_GAKU) URI_NUKI_GAKU
,MS70.TOKUI_RYA
FROM
DU0010 DU10
LEFT JOIN MS0070 MS70
ON MS70.TOKUI1_KEY = DU10.TOKUI1_KEY
AND MS70.TOKUI2_CD = '0000'
AND MS70.TOKUI3_CD = '0000'
WHERE
(DU10.URI_YMD LIKE :TARGET_YM ||'%' OR DU10.URI_YMD LIKE :LAST_YEAR_YM ||'%')
AND DU10.LAST_DEN_FLG = '1'
AND DU10.LINE_CD < 60
--AND DU10.TOKUI1_CD = '57001'
GROUP BY
SUBSTR(DU10.U<PERSON>_<PERSON><PERSON>,0,6)
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,MS70.TOKUI_RYA
ORDER BY
SUBSTR(DU10.URI_YMD,0,6)
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,MS70.TOKUI_RYA