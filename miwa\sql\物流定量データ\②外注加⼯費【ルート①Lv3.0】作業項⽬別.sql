SELECT
TEMP.BUT_CENTER_KBN 物流センタ区分
,TEMP.BUT_CENTER 物流センタCD
,MS29.BUT_CENTER_RYA 物流センタ略称
,TEMP.FUTAN_LINE 負担ライン
,TEMP.SAKI_TOKUI1_CD 得意先一次店
,TEMP.TOKUI_RYA 得意先略称
,TEMP.KOMOKU_MEI_CD 項目名CD
,TEMP.項目名
--売上
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.売上 ELSE 0 END) 前年同月売上
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.売上 ELSE 0 END) 対象月売上
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.売上 ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.売上 ELSE 0 END) 前年差売上
,NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.売上 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.売上 ELSE 0 END),0) * 100, 2),0) 前年比売上
--外注加工費率
,NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.外注加工費 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.売上 ELSE 0 END),0),3),0) 前年同月外注加工費率
,NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.外注加工費 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.売上 ELSE 0 END),0),3),0) 対象月外注加工費率
,NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.外注加工費 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.売上 ELSE 0 END),0),3),0)
- NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.外注加工費 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.売上 ELSE 0 END),0),3),0) 前年差外注加工費率
--外注加工費
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.外注加工費 ELSE 0 END) 前年同月外注加工費
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.外注加工費 ELSE 0 END) 対象月外注加工費
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.外注加工費 ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.外注加工費 ELSE 0 END) 前年差外注加工費
,NVL(ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.外注加工費 ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.外注加工費 ELSE 0 END),0) * 100, 2),0) 前年比外注加工費
FROM
(
SELECT
DB80.KEIJO_YM
,DB80.BUT_CENTER_KBN
,DB80.BUT_CENTER
,DB80.FUTAN_LINE
,DB80.SAKI_TOKUI1_CD
,MS70.TOKUI_RYA
,DB80.KOMOKU_MEI_CD
,MS20.NAME AS 項目名
,SUM(DB80.URIAGE_GAKU) AS 売上
,SUM(DB80.KAKU_KAZEI_GAKU)+SUM(DB80.KAKU_HIKAZEI_GAKU) AS 外注加工費
FROM
DB0080 DB80
LEFT JOIN MS0070 MS70
ON MS70.TOKUI1_KEY = DB80.SAKI_TOKUI_KEY
AND MS70.TOKUI2_CD = '0000'
AND MS70.TOKUI3_CD = '0000'
LEFT JOIN MS0020 MS20
ON MS20.SHIKIBETU_CD = 'GKMOKU'
AND MS20.REC_KBN = '2'
AND MS20.CD = DB80.KOMOKU_MEI_CD
WHERE
DB80.KEIJO_YM IN (:TARGET_YM,:LAST_YEAR_YM)
GROUP BY
DB80.KEIJO_YM
,DB80.BUT_CENTER_KBN
,DB80.BUT_CENTER
,DB80.FUTAN_LINE
,DB80.SAKI_TOKUI1_CD
,MS70.TOKUI_RYA
,DB80.KOMOKU_MEI_CD
,MS20.NAME
) TEMP
LEFT JOIN MS0290 MS29
ON MS29.BUT_CENTER_KBN = TEMP.BUT_CENTER_KBN
AND MS29.BUT_CENTER = TEMP.BUT_CENTER
GROUP BY
TEMP.BUT_CENTER_KBN
,TEMP.BUT_CENTER
,MS29.BUT_CENTER_RYA
,TEMP.FUTAN_LINE
,TEMP.SAKI_TOKUI1_CD
,TEMP.TOKUI_RYA
,TEMP.KOMOKU_MEI_CD
,TEMP.項目名
ORDER BY
TEMP.BUT_CENTER_KBN
,TEMP.BUT_CENTER
,TEMP.FUTAN_LINE
,TEMP.SAKI_TOKUI1_CD
,TEMP.TOKUI_RYA
,TEMP.KOMOKU_MEI_CD
,TEMP.項目名