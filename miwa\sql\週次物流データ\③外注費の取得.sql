select
CAL.CD as 識別CD
,CAL.NAME as 対象期間
,NVL( SUM(DB80.KAKU_KAZEI_GAKU)+SUM(DB80.KAKU_HIKAZEI_GAKU), 0) as 外注加工費
from
(
select 1 as CD, '今週' as NAME, :WEEK_FROM as FROMDATE, :WEEK_TO as TODATE from dual union all
select 2 as CD, '前年同週' as NAME, :LAST_YEAR_WEEK_FROM as FROMDATE, :LAST_YEAR_WEEK_TO as TODATE from dual union all
select 3 as CD, '前週' as NAME, :LAST_WEEK_FROM as FROMDATE, :LAST_WEEK_TO as TODATE from dual union all
select 4 as CD, '当月累積' as NAME, :MONTH_FROM as FROMDATE, :MONTH_TO as TODATE from dual union all
select 5 as CD, '前年同月' as NAME, :LAST_YEAR_MONTH_FROM as FROMDATE, :LAST_YEAR_MONTH_TO as TODATE from dual
) CAL
left join DB0080 DB80
on DB80.YMD between CAL.FROMDATE and CAL.TODATE
group by
CD
,NAME
order by
CD
,NAME