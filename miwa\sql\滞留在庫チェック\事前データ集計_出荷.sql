INSERT INTO MIWA_T920
(
<PERSON>UT_CENTER
,<PERSON><PERSON>_SHO_CD
,<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Y<PERSON>
,BARA_SU
,TOROK<PERSON>_YMD
,TOROKU_TIME
,TOROKU_CD
,TOROKU_PG
,<PERSON><PERSON><PERSON><PERSON>_YMD
,KOS<PERSON><PERSON>_TIME
,K<PERSON>HIN_CD
,KOSHIN_PG
,KOSHIN_KAISU
)
SELECT
DU70.CD_CNT BUT_CENTER
,DU70.CD_ITEM SHI_SHO_CD
,DU70.NO_LOT SHOMI_KIGEN_YMD
,SUM(DU70.NUM_SHPPLN) BARA_SU
,:TOROKU_YMD TOROKU_YMD
,:TOROKU_TIME TOROKU_TIME
,:TOROKU_CD TOROKU_CD
,:TOROKU_PG TOROKU_PG
,:KOSHIN_YMD KOSHIN_YMD
,:KOSHIN_TIME KOSHIN_TIME
,:KOSHIN_CD KOSHIN_CD
,:<PERSON><PERSON><PERSON><PERSON>_PG KOSHIN_PG
,:<PERSON><PERSON><PERSON><PERSON>_KAISU KOSHIN_KAISU
FROM
MIYATA_KIKAN.DU0070 DU70
WHERE
DU70.YMD_SHP BETWEEN :DUFROM AND :TODAY
GROUP BY
DU70.CD_CNT
,DU70.CD_ITEM
,DU70.NO_LOT