SELECT
NVL(UNCHIN.物流センタ区分, URIAGE.SHUKKA_BUT_CENTER_KBN) 物流センタ区分
,NVL(UNCHIN.物流センタCD, URIAGE.SHUKKA_BUT_CENTER) 物流センタCD
,MS29.BUT_CENTER_RYA 物流センタ略称
,NVL(UNCHIN.負担ライン, URIAGE.LINE_CD) 負担ライン
,UNCHIN.得意先一次店
,UNCHIN.得意先略称
,UNCHIN.請求書グループCD
,UNCHIN.請求書グループ略称
,UNCHIN.運送店CD
,UNCHIN.運送店名
,UNCHIN.運賃表CD
,UNCHIN.運賃表名
,UNCHIN.計上区分
,UNCHIN.計上区分名
,UNCHIN.作業形態CD
,UNCHIN.作業形態名
,UNCHIN.作業形態②CD
,UNCHIN.作業形態②名
,NVL(URIAGE.前年同月売上,0) 前年同月売上
,NVL(URIAGE.対象月売上,0) 対象月売上
,NVL(URIAGE.前年差売上,0) 前年差売上
,NVL(URIAGE.前年比売上,0) 前年比売上
,NVL(ROUND(NVL(UNCHIN.前年同月運賃,0) / NULLIF(URIAGE.前年同月売上,0),3),0) 前年同月運搬梱包費率
,NVL(ROUND(UNCHIN.対象月運賃 / NULLIF(URIAGE.対象月売上,0),3),0) 対象年月運搬梱包費率
,NVL(ROUND(UNCHIN.対象月運賃 / NULLIF(URIAGE.対象月売上,0),3),0)
- NVL(ROUND(NVL(UNCHIN.前年同月運賃,0) / NULLIF(URIAGE.前年同月売上,0),3),0) 前年差運搬梱包費率
,NVL(UNCHIN.前年同月運賃,0) 前年同月運賃
,NVL(UNCHIN.対象月運賃,0) 対象月運賃
,NVL(UNCHIN.前年差運賃,0) 前年差運賃
,NVL(UNCHIN.前年比運賃,0) 前年比運賃
,NVL(UNCHIN.前年同月才数,0) 前年同月才数
,NVL(UNCHIN.対象月才数,0) 対象月才数
,NVL(UNCHIN.前年差才数,0) 前年差才数
,NVL(UNCHIN.前年比才数,0) 前年比才数
,NVL(UNCHIN.前年同月数量,0) 前年同月数量
,NVL(UNCHIN.対象月数量,0) 対象月数量
,NVL(UNCHIN.前年差数量,0) 前年差数量
,NVL(UNCHIN.前年比数量,0) 前年比数量
,NVL(UNCHIN.前年同月運賃明細数,0) 前年同月運賃明細数
,NVL(UNCHIN.対象月運賃明細数,0) 対象月運賃明細数
,NVL(UNCHIN.前年差運賃明細数,0) 前年差運賃明細数
,NVL(UNCHIN.前年比運賃明細数,0) 前年比運賃明細数
,UNCHIN.先物流センタ区分
,UNCHIN.先物流センタCD
FROM
(
SELECT
TEMP.MOTO_BUT_CENTER_KBN 物流センタ区分
,TEMP.MOTO_BUT_CENTER_CD 物流センタCD
,TEMP.SAKI_BUT_CENTER_KBN 先物流センタ区分
,TEMP.SAKI_BUT_CENTER_CD 先物流センタCD
,TEMP.FUTAN_LINE 負担ライン
,TEMP.FUTAN_TOKUI1_CD 得意先一次店
,TEMP.TOKUI_RYA 得意先略称
,TEMP.SEIKYU_GRP_CD 請求書グループCD
,TEMP.SEIKYU_GRP_RYA_MEI 請求書グループ略称
,TEMP.UNSOTEN_CD 運送店CD
,TEMP.UNSOTEN_MEI 運送店名
,TEMP.UNCHINHYO_CD 運賃表CD
,TEMP.UNCHINHYO_MEI 運賃表名
,TEMP.KEIJO_KBN 計上区分
,TEMP.KK_NAME 計上区分名
,TEMP.SAGYO_KEITAI 作業形態CD
,TEMP.SK1_NAME 作業形態名
,TEMP.SAGYO_KEITAI2 作業形態②CD
,TEMP.SK2_NAME 作業形態②名
--運賃
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.UNCHIN ELSE 0 END) 前年同月運賃
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.UNCHIN ELSE 0 END) 対象月運賃
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.UNCHIN ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.UNCHIN ELSE 0 END) 前年差運賃
,ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.UNCHIN ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.UNCHIN ELSE 0 END),0) * 100, 2) 前年比運賃
--才数
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SAISU ELSE 0 END) 前年同月才数
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SAISU ELSE 0 END) 対象月才数
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SAISU ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SAISU ELSE 0 END) 前年差才数
,ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SAISU ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SAISU ELSE 0 END),0) * 100, 2) 前年比才数
--数量
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SURYO ELSE 0 END) 前年同月数量
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SURYO ELSE 0 END) 対象月数量
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SURYO ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SURYO ELSE 0 END) 前年差数量
,ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.SURYO ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.SURYO ELSE 0 END),0) * 100, 2) 前年比数量
--運賃明細数
,SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.CNT ELSE 0 END) 前年同月運賃明細数
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.CNT ELSE 0 END) 対象月運賃明細数
,SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.CNT ELSE 0 END)
- SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.CNT ELSE 0 END) 前年差運賃明細数
,ROUND(SUM(CASE WHEN TEMP.KEIJO_YM = :TARGET_YM THEN TEMP.CNT ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.KEIJO_YM = :LAST_YEAR_YM THEN TEMP.CNT ELSE 0 END),0) * 100, 2) 前年比運賃明細数
FROM
(
SELECT
DB40.KEIJO_YM
,DB40.MOTO_BUT_CENTER_KBN
,DB40.MOTO_BUT_CENTER_CD
,DB40.SAKI_BUT_CENTER_KBN
,DB40.SAKI_BUT_CENTER_CD
,DB40.FUTAN_LINE
,DB40.FUTAN_TOKUI1_CD
,MS70.TOKUI_RYA
,DB40.SEIKYU_GRP_CD
,MS116.SEIKYU_GRP_RYA_MEI
,DB40.UNSOTEN_CD
,MS44.UNSOTEN_MEI
,MS44.UNCHINHYO_CD
,MS54.UNCHINHYO_MEI
,DB40.KEIJO_KBN
,MS20_KK.NAME AS KK_NAME
,DB40.SAGYO_KEITAI
,MS20_SK1.NAME AS SK1_NAME
,DB40.SAGYO_KEITAI2
,MS20_SK2.NAME AS SK2_NAME
,SUM(DB40.KAKU_UNCHIN)+SUM(DB40.KAKU_UNCHIN_ZIPPI) AS UNCHIN
,SUM(DB40.SAISU) AS SAISU
,SUM(DB40.SURYO) AS SURYO
,COUNT(*) AS CNT
FROM
DB0040 DB40
LEFT JOIN MS0070 MS70
ON MS70.TOKUI1_KEY = DB40.FUTAN_TOKUI1_KEY
AND MS70.TOKUI2_CD = '0000'
AND MS70.TOKUI3_CD = '0000'
LEFT JOIN MS1160 MS116
ON DB40.SEIKYU_GRP_CD = MS116.SEIKYU_GRP_CD
LEFT JOIN MS0440 MS44
ON DB40.UNSOTEN_CD = MS44.UNSOTEN_CD
LEFT JOIN MS0540 MS54
ON MS54.UNCHINHYO_CD = MS44.UNCHINHYO_CD
LEFT JOIN MS0020 MS20_KK
ON MS20_KK.SHIKIBETU_CD = 'UKJKBN'
AND MS20_KK.REC_KBN = '2'
AND MS20_KK.CD = DB40.KEIJO_KBN
LEFT JOIN MS0020 MS20_SK1
ON MS20_SK1.SHIKIBETU_CD = 'SAGTAI'
AND MS20_SK1.REC_KBN = '2'
AND MS20_SK1.CD = DB40.SAGYO_KEITAI
LEFT JOIN MS0020 MS20_SK2
ON MS20_SK2.SHIKIBETU_CD = 'SAGTA2'
AND MS20_SK2.REC_KBN = '2'
AND MS20_SK2.CD = DB40.SAGYO_KEITAI2
WHERE
DB40.KEIJO_YM IN (:TARGET_YM,:LAST_YEAR_YM)
--AND DB40.FUTAN_TOKUI1_CD = '45017'
GROUP BY
DB40.KEIJO_YM
,DB40.MOTO_BUT_CENTER_KBN
,DB40.MOTO_BUT_CENTER_CD
,DB40.SAKI_BUT_CENTER_KBN
,DB40.SAKI_BUT_CENTER_CD
,DB40.FUTAN_LINE
,DB40.FUTAN_TOKUI1_CD
,MS70.TOKUI_RYA
,DB40.SEIKYU_GRP_CD
,MS116.SEIKYU_GRP_RYA_MEI
,DB40.UNSOTEN_CD
,MS44.UNSOTEN_MEI
,MS44.UNCHINHYO_CD
,MS54.UNCHINHYO_MEI
,DB40.KEIJO_KBN
,MS20_KK.NAME
,DB40.SAGYO_KEITAI
,MS20_SK1.NAME
,DB40.SAGYO_KEITAI2
,MS20_SK2.NAME
) TEMP
GROUP BY
TEMP.MOTO_BUT_CENTER_KBN
,TEMP.MOTO_BUT_CENTER_CD
,TEMP.SAKI_BUT_CENTER_KBN
,TEMP.SAKI_BUT_CENTER_CD
,TEMP.FUTAN_LINE
,TEMP.FUTAN_TOKUI1_CD
,TEMP.TOKUI_RYA
,TEMP.SEIKYU_GRP_CD
,TEMP.SEIKYU_GRP_RYA_MEI
,TEMP.UNSOTEN_CD
,TEMP.UNSOTEN_MEI
,TEMP.UNCHINHYO_CD
,TEMP.UNCHINHYO_MEI
,TEMP.KEIJO_KBN
,TEMP.KK_NAME
,TEMP.SAGYO_KEITAI
,TEMP.SK1_NAME
,TEMP.SAGYO_KEITAI2
,TEMP.SK2_NAME
) UNCHIN
FULL JOIN
(
SELECT
TEMP.SHUKKA_BUT_CENTER_KBN
,TEMP.SHUKKA_BUT_CENTER
,TEMP.LINE_CD
,TEMP.TOKUI1_CD
,TEMP.UNSOTEN_CD
,SUM(CASE WHEN TEMP.URI_YM = :LAST_YEAR_YM THEN TEMP.URI_GAKU ELSE 0 END) 前年同月売上
,SUM(CASE WHEN TEMP.URI_YM = :TARGET_YM THEN TEMP.URI_GAKU ELSE 0 END) 対象月売上
,SUM(CASE WHEN TEMP.URI_YM = :TARGET_YM THEN TEMP.URI_GAKU ELSE 0 END)
- SUM(CASE WHEN TEMP.URI_YM = :LAST_YEAR_YM THEN TEMP.URI_GAKU ELSE 0 END) 前年差売上
,ROUND(SUM(CASE WHEN TEMP.URI_YM = :TARGET_YM THEN TEMP.URI_GAKU ELSE 0 END)
/ NULLIF(SUM(CASE WHEN TEMP.URI_YM = :LAST_YEAR_YM THEN TEMP.URI_GAKU ELSE 0 END),0) * 100, 2) 前年比売上
FROM
(
SELECT
SUBSTR(DU20.URI_YMD,0,6) AS URI_YM
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
,DU20.LINE_CD
,DU20.TOKUI1_CD
,DU20.UNSOTEN_CD
,SUM(DU20.URI_NUKI_GAKU) AS URI_GAKU
FROM
DU0020 DU20
WHERE
(DU20.URI_YMD LIKE :TARGET_YM ||'%' OR DU20.URI_YMD LIKE :LAST_YEAR_YM ||'%')
AND DU20.LAST_DEN_FLG = '1'
AND DU20.URI_HEN_KBN = '12'
--AND DU20.TOKUI1_CD='45017'
GROUP BY
SUBSTR(DU20.URI_YMD,0,6)
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
,DU20.LINE_CD
,DU20.TOKUI1_CD
,DU20.UNSOTEN_CD
) TEMP
GROUP BY
TEMP.SHUKKA_BUT_CENTER_KBN
,TEMP.SHUKKA_BUT_CENTER
,TEMP.LINE_CD
,TEMP.TOKUI1_CD
,TEMP.UNSOTEN_CD
) URIAGE
ON UNCHIN.物流センタ区分 = URIAGE.SHUKKA_BUT_CENTER_KBN
AND UNCHIN.物流センタCD = URIAGE.SHUKKA_BUT_CENTER
AND UNCHIN.負担ライン = URIAGE.LINE_CD
AND UNCHIN.得意先一次店 = URIAGE.TOKUI1_CD
AND UNCHIN.運送店CD = URIAGE.UNSOTEN_CD
LEFT JOIN MS0290 MS29
ON MS29.BUT_CENTER_KBN = NVL(UNCHIN.物流センタ区分, URIAGE.SHUKKA_BUT_CENTER_KBN)
AND MS29.BUT_CENTER = NVL(UNCHIN.物流センタCD, URIAGE.SHUKKA_BUT_CENTER)
ORDER BY
NVL(UNCHIN.物流センタ区分, URIAGE.SHUKKA_BUT_CENTER_KBN)
,NVL(UNCHIN.物流センタCD, URIAGE.SHUKKA_BUT_CENTER)
,NVL(UNCHIN.負担ライン, URIAGE.LINE_CD)
,UNCHIN.得意先一次店
,UNCHIN.請求書グループCD
,UNCHIN.運送店CD
,UNCHIN.計上区分
,UNCHIN.作業形態CD
,UNCHIN.作業形態②CD
-- 前年差 * UNCHIN.対象月運賃
-- DESC NULLS LAST