select
CD as 識別CD
,NAME as 対象期間
,sum(SAISU) as 出荷数量
,sum(case when CLS_SLIP in ('1') then SAISU else 0 end ) 納品 -- 1: 通常出荷
,sum(case when CLS_SLIP in ('2', '3', '4') then SAISU else 0 end ) 転送 -- 2～4: 転送
,sum(case when <PERSON><PERSON>ON_BUNRUI in ('60') then SAISU else 0 end ) 内輸入数 -- 60: 輸入品
from (
select
CAL.CD
,CAL.NAME
,DU70.CLS_SLIP
,MS90.BUMON_BUNRUI
,SUM(
case DU70.CLS_ORD_UNIT
when 'A' then MS90.AW_SAISU * (du70.NUM_SHP / (MS90.IRISU1 * MS90.IRISU2) )
when 'C' then MS90.CS_SAISU * (du70.NUM_SHP / (MS90.IRISU1 * MS90.IRISU2) )
when 'B' then MS90.BL_SAISU * (du70.NUM_SHP / (MS90.IRISU1) )
when 'P' then MS90.PS_SAISU * (du70.NUM_SHP)
else 0
end
) as SAISU
from (
select 1 as CD, '今週' as NAME, :WEEK_FROM as FROMDATE, :WEEK_TO as TODATE from dual union all
select 2 as CD, '前年同週' as NAME, :LAST_YEAR_WEEK_FROM as FROMDATE, :LAST_YEAR_WEEK_TO as TODATE from dual union all
select 3 as CD, '前週' as NAME, :LAST_WEEK_FROM as FROMDATE, :LAST_WEEK_TO as TODATE from dual union all
select 4 as CD, '当月累積' as NAME, :MONTH_FROM as FROMDATE, :MONTH_TO as TODATE from dual union all
select 5 as CD, '前年同月' as NAME, :LAST_YEAR_MONTH_FROM as FROMDATE, :LAST_YEAR_MONTH_TO as TODATE from dual
) CAL
left join DU0070 DU70
on DU70.YMD_RCV between CAL.FROMDATE and CAL.TODATE
left join MS0090 MS90
on DU70.CD_ITEM = MS90.SHI_SHO_CD
group by
CAL.CD
,CAL.NAME
,DU70.CLS_SLIP
,MS90.BUMON_BUNRUI
)
group by
CD
,NAME
order by
CD
,NAME