SELECT
SUBSTR(DH60.TEN_YMD,0,6) TEN_YM
,DH60.MOTO_BUT_CENTER_KBN
,DH60.MOTO_BUT_CENTER
,DH60.SAKI_BUT_CENTER_KBN
,DH60.SHI_SHO_CD
,SUM(DH60.TEN_SU) TEN_SU
,DH60.TANI
,TRUNC( SUM(
CASE
WHEN DH60.TANI = 'A'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DH60.TEN_BARA_SU / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DH60.TANI = 'C'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DH60.TEN_BARA_SU / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DH60.TANI = 'B'
THEN ((MS90.BL_SIZE_OKU / 1000) * (MS90.BL_SIZE_HABA / 1000) * (MS90.BL_SIZE_TAKA / 1000) * (DH60.TEN_BARA_SU / (MS90.IRISU1) ) / 0.0278)
WHEN DH60.TANI = 'P'
THEN ((MS90.PS_SIZE_OKU / 1000) * (MS90.PS_SIZE_HABA / 1000) * (MS90.PS_SIZE_TAKA / 1000) * (DH60.TEN_BARA_SU) / 0.0278)
ELSE 0
END
), 2) AS 才数
FROM
DH0060 DH60
LEFT JOIN MS0090 MS90
ON MS90.SHI_SHO_CD = DH60.SHI_SHO_CD
WHERE
(DH60.TEN_YMD LIKE :TARGET_YM ||'%' OR DH60.TEN_YMD LIKE :LAST_YEAR_YM ||'%')
GROUP BY
SUBSTR(DH60.TEN_YMD,0,6)
,DH60.MOTO_BUT_CENTER_KBN
,DH60.MOTO_BUT_CENTER
,DH60.SAKI_BUT_CENTER_KBN
,DH60.SHI_SHO_CD
,DH60.TANI
ORDER BY
SUBSTR(DH60.TEN_YMD,0,6)
,DH60.MOTO_BUT_CENTER_KBN
,DH60.MOTO_BUT_CENTER
,DH60.SAKI_BUT_CENTER_KBN
,DH60.SHI_SHO_CD
,DH60.TANI