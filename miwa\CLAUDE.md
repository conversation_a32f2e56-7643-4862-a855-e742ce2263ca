# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Japanese business data processing system called "miwa" that handles logistics and inventory data for Miyata corporation. The system processes SQL queries and generates reports using Excel templates.

## Development Environment

This project uses `uv` for Python dependency management (version 0.8.12+). The project requires Python 3.13 or higher.

### Common Commands

```bash
# Run the main application
uv run python main.py

# Install dependencies (if any are added to pyproject.toml)
uv sync

# Add new dependencies
uv add <package-name>
```

## Project Structure

The codebase is organized into three main components:

1. **SQL Queries** (`sql/` directory):
   - **在庫データ定期スナップショット**: Inventory snapshot queries for DZ0030 and DZ0390 tables
   - **滞留在庫チェック**: Stagnant inventory checking with data aggregation queries for 仕入 (purchasing), 出荷 (shipping), 受注 (orders), and 転送 (transfers)
   - **物流定量データ**: Logistics quantitative data with numbered queries (①-⑭) for various metrics including freight costs, outsourcing costs, sales data, and inventory snapshots
   - **週次物流データ**: Weekly logistics data queries for shipping capacity, packaging costs, and center-specific metrics

2. **Excel Templates** (`template/` directory):
   - Pre-formatted Excel files for various reports including daily sales reports, inventory checks, and logistics data

3. **Main Application** (`main.py`):
   - Entry point for the application (currently a minimal placeholder)

## Database Context

The SQL queries interact with the MIYATA_KIKAN database schema, working with tables such as:
- DH0020, DH0040: Transaction/movement data
- DS0020: Center/location data
- DZ0030, DZ0390: Inventory snapshots
- MS0090, MS0890: Master data tables
- DU0010, DU0020, DU0070: Sales and shipping data
- DB0040: Delivery destination data

Most queries use parameterized inputs (e.g., :TOROKU_YMD, :CENTER_CD) that would need to be provided at runtime.

## Important Notes

- All SQL files use Japanese column names and comments
- The system appears to be designed for batch processing of logistics and inventory data
- Templates suggest the system generates periodic reports (daily, weekly, monthly)
- The bat file in 在庫データ定期スナップショット suggests automated batch processing capabilities