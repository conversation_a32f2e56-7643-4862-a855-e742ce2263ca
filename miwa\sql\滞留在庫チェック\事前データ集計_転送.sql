INSERT INTO MIWA_T930
(
<PERSON><PERSON><PERSON>SHO_CD
,<PERSON><PERSON><PERSON>_<PERSON>IGEN
,BUT_CENTER
,NYU_YOTEI_YMD
,TEN_SHUKKA_J_BARA_SU
,TOROKU_YMD
,TOROKU_TIME
,TOROKU_CD
,TOROKU_PG
,K<PERSON><PERSON><PERSON>_YMD
,KOSHIN_TIME
,KOSHIN_CD
,KOSHIN_PG
,KOSHIN_KAISU
)
SELECT
DH60.SHI_SHO_CD SHI_SHO_CD
,DU70.NO_LOT_R SHOMI_KIGEN
,DH60.SAKI_BUT_CENTER BUT_CENTER
,MIN(DH60.NYU_YOTEI_YMD) NYU_YOTEI_YMD
,SUM(DH60.TEN_SHUKKA_J_BARA_SU) TEN_SHUKKA_J_BARA_SU
,:TOROKU_YMD TOROKU_YMD
,:TOROKU_TIME TOROKU_TIME
,:TOROKU_CD TOROKU_CD
,:TORO<PERSON>U_PG TOROKU_PG
,:<PERSON><PERSON><PERSON>IN_<PERSON><PERSON>_YMD
,:<PERSON><PERSON>HIN_<PERSON><PERSON><PERSON>_TIME
,:<PERSON>OSHIN_CD KOSHIN_CD
,:<PERSON><PERSON><PERSON><PERSON>_PG KOSHIN_PG
,:KOSHIN_KAISU KOSHIN_KAISU
FROM
MIYATA_KIKAN.DH0060 DH60
LEFT JOIN MIYATA_KIKAN.DU0070 DU70
ON DH60.SHUKKA_SHIJI_BAT_NO = DU70.NO_INST_BATCH
AND DH60.SHUKKA_SHIJI_NO = DU70.NO_INST
AND DH60.SHI_SHO_CD = DU70.CD_ITEM
LEFT JOIN MIYATA_KIKAN.DZ0030 DZ30
ON DH60.SAKI_BUT_CENTER_KBN = DZ30.BUT_CENTER_KBN
AND DH60.SAKI_BUT_CENTER = DZ30.BUT_CENTER
AND DH60.SHI_SHO_CD = DZ30.SHI_SHO_CD
AND DZ30.SHOMI_KIGEN_YMD = DU70.NO_LOT_R
WHERE 1=1
AND DH60.KANNO_FLG = '1'
AND DH60.TEN_SHUKKA_J_BARA_SU > 0
AND DZ30.ZAIKO_SU > 0
GROUP BY
DH60.SHI_SHO_CD
,DU70.NO_LOT_R
,DH60.SAKI_BUT_CENTER