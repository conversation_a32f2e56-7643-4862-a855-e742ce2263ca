"""
週次物流データ作成
"""

import logging
import logging.config
from datetime import datetime, timedelta
from pathlib import Path

import yaml  # type: ignore
from dotenv import load_dotenv

# ユーティリティモジュールのインポート
from utils.database import get_db_connection, read_sql_file
from utils.excel import ExcelWriter

# 環境変数の読み込み
load_dotenv()

# ロギング設定の読み込み
with open("logging_config.yaml", "r", encoding="utf-8") as f:
    config = yaml.safe_load(f.read())
    logging.config.dictConfig(config)

# ロガーの取得
logger = logging.getLogger("oracle_app")


def main():
    """メイン処理"""
    # パラメータ日付の取得
    base_date = datetime.now()

    # 基準日の週の開始日（月曜日）を取得
    days_since_monday = base_date.weekday()
    week_start = base_date - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)

    # 前週
    last_week_start = week_start - timedelta(days=7)
    last_week_end = week_end - timedelta(days=7)

    # 前年同週
    last_year_week_start = week_start.replace(year=week_start.year - 1)
    last_year_week_end = week_end.replace(year=week_end.year - 1)

    # 当月累積（月初から基準日まで）
    month_start = base_date.replace(day=1)
    month_end = base_date

    # 前年同月
    last_year_month_start = month_start.replace(year=month_start.year - 1)
    last_year_month_end = month_end.replace(year=month_end.year - 1)

    # パラメータ
    params_for_file = {
        "WEEK_FROM": week_start,
        "WEEK_TO": week_end,
        "LAST_YEAR_WEEK_FROM": last_year_week_start,
        "LAST_YEAR_WEEK_TO": last_year_week_end,
        "LAST_WEEK_FROM": last_week_start,
        "LAST_WEEK_TO": last_week_end,
        "MONTH_FROM": month_start,
        "MONTH_TO": month_end,
        "LAST_YEAR_MONTH_FROM": last_year_month_start,
        "LAST_YEAR_MONTH_TO": last_year_month_end,
    }
    logger.info(f"params_for_file: {params_for_file}")

    # outputディレクトリの作成
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)

    # データベース接続（コンテキストマネージャーを使用）
    try:
        with get_db_connection() as db:
            # ①出荷才数の取得
            logger.info("①出荷才数の取得")
            # SQLファイルから読み込む
            sql_file_path = Path("sql/週次物流データ/①出荷才数の取得.sql")
            if sql_file_path.exists():
                sql_from_file = read_sql_file(sql_file_path)
            df = db.execute_query(sql_from_file, params_for_file)

            # 結果の表示
            logger.info("取得したデータ:")
            logger.info(f"\n{df.head()}")

            # Excel出力
            template_path = Path("template/Template_Miwa9060_週次物流データ.xlsx")
            if template_path.exists():
                output_template = (
                    output_dir
                    / f"週次物流データ_{datetime.now().strftime('%Y%m%d')}.xlsx"
                )
                ExcelWriter.write_to_template(
                    df,
                    template_path,
                    output_template,
                    sheet_name="データシート",
                    start_row=12,  # データ開始行
                    start_col=2,  # データ開始列
                )

    except Exception as e:
        logger.error(f"エラーが発生しました: {e}")


if __name__ == "__main__":
    # 注意: 実行前に .env ファイルの設定が必要です
    main()
