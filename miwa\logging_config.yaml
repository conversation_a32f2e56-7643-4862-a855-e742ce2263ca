version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.TimedRotatingFileHandler
    level: INFO
    formatter: standard
    filename: logs/oracle_app.log
    when: midnight  # 毎日深夜0時にローテーション
    interval: 1  # 1日ごと
    backupCount: 30  # 30日分のログを保持
    encoding: utf-8

loggers:
  oracle_app:
    level: INFO
    handlers: [console, file]
    propagate: false

root:
  level: INFO
  handlers: [console, file]