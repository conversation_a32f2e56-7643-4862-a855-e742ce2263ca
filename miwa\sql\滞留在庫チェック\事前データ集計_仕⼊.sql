INSERT INTO MIWA_T900
(
BUT_CENTER_KBN
,BUT_CENTER
,<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>
,SH<PERSON><PERSON>_YMD
,<PERSON><PERSON><PERSON>_CD
,SHOHIN_CD
,SHI_SHO_CD
,NOU_SU
,TORO<PERSON>U_YMD
,TOROKU_TIME
,TOROKU_CD
,TOROKU_PG
,KOSHIN_YMD
,KOSHIN_TIME
,KOSHIN_CD
,KOSHIN_PG
,KOSHIN_KAISU
)
SELECT
DS20.BUT_CENTER_KBN BUT_CENTER_KBN
,DS20.BUT_CENTER BUT_CENTER
,DH40.JI_SHOMI_KIGEN SHOMI_KIGEN
,MIN(DH40.SHIIRE_YMD) SHIIRE_YMD
,DH20.SHIIRE_CD SHIIRE_CD
,DH20.SHOHIN_CD SHOHIN_CD
,DH20.SHI_SHO_CD SHI_SHO_CD
,SUM(DH40.NOU_SU) NOU_SU
,:TOROKU_YMD TOROKU_YMD
,:TOROKU_TIME TOROKU_TIME
,:TOROKU_CD TOROKU_CD
,:TO<PERSON><PERSON>U_PG TOROKU_PG
,:K<PERSON>H<PERSON>_YMD KOSHIN_YMD
,:<PERSON><PERSON><PERSON>IN_TIME KOSHIN_TIME
,:KOSHIN_CD KOSHIN_CD
,:KOSHIN_PG KOSHIN_PG
,:KOSHIN_KAISU KOSHIN_KAISU
FROM
MIYATA_KIKAN.DH0020 DH20
INNER JOIN MIYATA_KIKAN.DH0040 DH40
ON DH20.NAI_HAC_DEN_NO = DH40.NAI_HAC_DEN_NO
AND DH20.HAC_DEN_NO = DH40.HAC_DEN_NO
AND DH20.HAC_LINE_NO = DH40.HAC_LINE_NO
INNER JOIN MIYATA_KIKAN.DS0020 DS20
ON DH20.NAI_HAC_DEN_NO = DS20.NAI_HAC_DEN_NO
AND DH20.HAC_DEN_NO = DS20.HAC_DEN_NO
AND DH20.HAC_LINE_NO = DS20.HAC_LINE_NO
WHERE 1=1
AND DS20.BUT_CENTER_KBN = '2'
AND DH40.SHIIRE_YMD BETWEEN :DSFROM AND :TODAY
AND DH40.SHIIRE_FLG = '1'
AND EXISTS ( SELECT 'X' FROM MIYATA_KIKAN.DZ0030 DZ30 WHERE DH20.SHIIRE_CD = DZ30.SHIIRE_CD )
AND DH40.JI_SHOMI_KIGEN IS NOT NULL
GROUP BY
DS20.BUT_CENTER_KBN
,DS20.BUT_CENTER
,DH40.JI_SHOMI_KIGEN
,DH20.SHIIRE_CD
,DH20.SHOHIN_CD
,DH20.SHI_SHO_CD