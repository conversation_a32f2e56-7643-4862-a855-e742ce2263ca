select
CD as 識別CD
,NAME as 対象期間
,SUM( NVL(DU10.URI_NUKI_GAKU, 0) * NVL(MS890.KINGAKU_RITU, 0) / 100) センター費
from
(
select 1 as CD, '今週' as NAME, :WEEK_FROM as FROMDATE, :WEEK_TO as TODATE from dual union all
select 2 as CD, '前年同週' as NAME, :LAST_YEAR_WEEK_FROM as FROMDATE, :LAST_YEAR_WEEK_TO as TODATE from dual union all
select 3 as CD, '前週' as NAME, :LAST_WEEK_FROM as FROMDATE, :LAST_WEEK_TO as TODATE from dual union all
select 4 as CD, '当月累積' as NAME, :MONTH_FROM as FROMDATE, :MONTH_TO as TODATE from dual union all
select 5 as CD, '前年同月' as NAME, :LAST_YEAR_MONTH_FROM as FROMDATE, :LAST_YEAR_MONTH_TO as TODATE from dual
) CA<PERSON>
left join DU0010 DU10
on DU10.URI_YMD between CAL.FROMDATE and CAL.TODATE
and DU10.LAST_DEN_FLG = '1'
left join MS0890 MS890
on MS890.TOKUI1_KEY = DU10.TOKUI1_KEY
and MS890.TOKUI2_CD = '0000'
and MS890.NEBIKI_SYURUI = '070'
and DU10.URI_YMD between MS890.TEKIYO_KIKAN_FR||'01' and MS890.TEKIYO_KIKAN_TO||'31'
group by
CD
,NAME
order by
CD
,NAME