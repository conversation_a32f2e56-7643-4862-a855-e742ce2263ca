SELECT
SUBSTR(DH20.NYU_KAKUTEI_YMD,0,6) NYU_KAKUTEI_YM
,DH20.NOU_BUT_CENTER_KBN
,DH20.NOU_BUT_CENTER
-- ,DH20.SHI_SHO_CD
-- ,SUM(DH20.NOU_BARA_SU)
-- ,DH20.TANI
,TRUNC( SUM(
CASE
WHEN DH20.TANI = 'A'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000)) * DH20.NOU_BARA_SU
WHEN DH20.TANI = 'C'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000)) * DH20.NOU_BARA_SU
WHEN DH20.TANI = 'B'
THEN ((MS90.BL_SIZE_OKU / 1000) * (MS90.BL_SIZE_HABA / 1000) * (MS90.BL_SIZE_TAKA / 1000)) * DH20.NOU_BARA_SU
WHEN DH20.TANI = 'P'
THEN ((MS90.PS_SIZE_OKU / 1000) * (MS90.PS_SIZE_HABA / 1000) * (MS90.PS_SIZE_TAKA / 1000)) * DH20.NOU_BARA_SU
ELSE 0
END
), 2) AS 才数
FROM
DH0020 DH20
LEFT JOIN MS0090 MS90
ON MS90.SHI_SHO_CD = DH20.SHI_SHO_CD
WHERE
(DH20.NYU_KAKUTEI_YMD LIKE :TARGET_YM ||'%' OR DH20.NYU_KAKUTEI_YMD LIKE :LAST_YEAR_YM ||'%')
AND DH20.KANNO_FLG = '1'
AND DH20.NOU_BUT_CENTER_KBN = '2'
GROUP BY
SUBSTR(DH20.NYU_KAKUTEI_YMD,0,6)
,DH20.NOU_BUT_CENTER_KBN
,DH20.NOU_BUT_CENTER
-- ,DH20.SHI_SHO_CD
-- ,DH20.TANI
ORDER BY
SUBSTR(DH20.NYU_KAKUTEI_YMD,0,6)
,DH20.NOU_BUT_CENTER_KBN
,DH20.NOU_BUT_CENTER
-- ,DH20.SHI_SHO_CD
-- ,DH20.TANI