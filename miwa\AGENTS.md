# Repository Guidelines

## Project Structure & Module Organization
Automation entry points live at the repository root. `weekly_logi_data.py` is the production pipeline for the weekly logistics report, `oracle_sample.py` stays as a minimal reference when bootstrapping new flows, and `main.py` is a generic stub for ad-hoc runs. Shared helpers reside in `utils/` (`database.py` handles Oracle connectivity, `excel.py` manages template-aware writes). SQL lives under `sql/` in Japanese-named folders that mirror delivered reports—store new queries beside the asset they feed. Excel templates are versioned in `template/`, while generated workbooks go to `output/` and rotating logs to `logs/`. Configure logging via `logging_config.yaml`, and load secrets from a local `.env` before touching Oracle.

## Build, Test, and Development Commands
- `uv sync`: install dependencies pinned by `pyproject.toml` / `uv.lock`.
- `uv run python weekly_logi_data.py`: execute the production pipeline end-to-end.
- `uv run python oracle_sample.py`: run the sample job when drafting new logic.
- `uv run python -m utils.database`: smoke-test Oracle connectivity with current environment variables.
- `uv run pytest`: execute the suite once `tests/` exists.
- `uv lock --upgrade`: refresh dependency pins after library updates.

## Coding Style & Naming Conventions
Target Python 3.13 and follow PEP 8: four-space indentation, `snake_case` for functions and variables, `PascalCase` for classes. Favor f-strings and descriptive loggers such as `logging.getLogger("oracle_app")`. Keep Oracle configuration centralized in `utils/database.py`, and retain Japanese docstrings or inline notes where they capture business rules.

## Testing Guidelines
Adopt `pytest` under a `tests/` directory that mirrors `utils/`. Name modules `test_<module>.py`, mock Oracle calls so runs stay deterministic, and cover both success and failure paths. For Excel logic, compare generated files against trimmed fixtures in `template/` or use snapshot helpers. Run `uv run pytest` before publishing and document special fixtures or setup steps in `tests/README.md`.

## Commit & Pull Request Guidelines
Follow the observed history by prefixing commit subjects with `miwa` plus a concise Japanese summary (e.g., `miwa ログ出力を追加`). Keep commits focused, reference affected SQL or template assets in the body, and link issue IDs when available. Pull requests should explain scope, expected outputs, and include before/after Excel screenshots when layouts shift. Confirm that `logging_config.yaml`, `.env`, and other secret-bearing files remain untouched before requesting review.

## Environment & Security Notes
Store Oracle credentials exclusively in `.env`; never hardcode DSNs or passwords. Ensure `logs/` and `output/` stay gitignored before running jobs, and archive or purge generated workbooks after handoff to stakeholders.
