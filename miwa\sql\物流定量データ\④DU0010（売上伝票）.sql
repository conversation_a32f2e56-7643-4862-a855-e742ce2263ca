SELECT
SUBSTR(DU10.U<PERSON>_YMD,0,6) URI_YM
,DU10.URI_HEN_KBN
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,SUM(DU20.URI_NUKI_GAKU) URI_NUKI_GAKU
,MS70.TOKUI_RYA
,TRUNC( SUM(
CASE
WHEN DU20.TANI = 'A'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DU20.BARA_SU / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DU20.TANI = 'C'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DU20.BARA_SU / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DU20.TANI = 'B'
THEN ((MS90.BL_SIZE_OKU / 1000) * (MS90.BL_SIZE_HABA / 1000) * (MS90.BL_SIZE_TAKA / 1000) * (DU20.BARA_SU / (MS90.IRISU1) ) / 0.0278)
WHEN DU20.TANI = 'P'
THEN ((MS90.PS_SIZE_OKU / 1000) * (MS90.PS_SIZE_HABA / 1000) * (MS90.PS_SIZE_TAKA / 1000) * (DU20.BARA_SU) / 0.0278)
ELSE 0
END
), 2) AS 才数
FROM
DU0010 DU10
LEFT JOIN DU0020 DU20
ON DU10.URI_DEN_NO = DU20.URI_DEN_NO
LEFT JOIN MS0090 MS90
ON DU20.SHIIRE_CD = MS90.SHIIRE_CD
AND DU20.SHOHIN_CD = MS90.SHOHIN_CD
LEFT JOIN MS0070 MS70
ON MS70.TOKUI1_KEY = DU10.TOKUI1_KEY
AND MS70.TOKUI2_CD = '0000'
AND MS70.TOKUI3_CD = '0000'
WHERE
(DU10.URI_YMD LIKE :TARGET_YM ||'%' OR DU10.URI_YMD LIKE :LAST_YEAR_YM ||'%')
AND DU10.LAST_DEN_FLG = '1'
AND DU10.LINE_CD < 60
-- AND DU10.TOKUI1_CD = '57001'
GROUP BY
SUBSTR(DU10.URI_YMD,0,6)
,DU10.URI_HEN_KBN
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,MS70.TOKUI_RYA
ORDER BY
SUBSTR(DU10.URI_YMD,0,6)
,DU10.URI_HEN_KBN
,DU10.TOKUI1_CD
,DU10.SHUKKA_BUT_CENTER_KBN
,DU10.SHUKKA_BUT_CENTER
,MS70.TOKUI_RYA