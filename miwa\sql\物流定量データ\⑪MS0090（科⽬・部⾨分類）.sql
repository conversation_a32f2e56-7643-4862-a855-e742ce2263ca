SELECT DISTINCT
MS90.<PERSON><PERSON>_SHO_CD
,MS90.<PERSON><PERSON><PERSON><PERSON>_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
FROM
MS0090 MS90
LEFT JOIN MS0020 MS20_SIHABU
ON MS20_SIHABU.SHI<PERSON><PERSON>ETU_CD = 'SIHABU'
AND MS20_SIHABU.REC_KBN = '2'
AND MS20_SIHABU.CD = MS90.KAMOKU_BUNRUI
LEFT JOIN MS0020 MS20_BUMBUN
ON MS20_BUMBUN.SHIKIBETU_CD = 'BUMBUN'
AND MS20_BUMBUN.REC_KBN = '2'
AND MS20_BUMBUN.CD = MS90.BUMON_BUNRUI
WHERE
MS90.SHUBAI_YMD IS NULL
AND MS90.KAMOKU_BUNRUI IN ('10','20','30')
ORDER BY
MS90.SHI_SHO_CD