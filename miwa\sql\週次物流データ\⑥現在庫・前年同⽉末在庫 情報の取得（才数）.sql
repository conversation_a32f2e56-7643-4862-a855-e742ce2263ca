select
BUT.BUT_CENTER as 物流センタCD
,NVL(GENZAIKO.SAISU, 0) as 現在庫P才数
,NVL(ZENNEN_ZAIKO.SAISU, 0) as 前年同月末P才数
,NVL(ZENGETU_ZAIKO.SAISU, 0) as 前月末P才数
,NVL(IN_SAISU.SAISU, 0) as 当月入庫P才数
,NVL(OUT_SAISU.SAISU, 0) as 当月出庫P才数
from MS0290 BUT
-- 現在庫 --在庫データから算出
left join (
select
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
,sum(ZAIKO.ZAIKO_SU * SHOHIN.PS_SAISU) AS SAISU
from DZ0030 ZAIKO
inner join MS0090 SHOHIN
on ZAIKO.SHIIRE_CD = SHOHIN.SHIIRE_CD
and ZAIKO.SHOHIN_CD = SHOHIN.SHOHIN_CD
where ZAIKO.BUT_CENTER_KBN = '2'
group by
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
) GENZAIKO
on BUT.BUT_CENTER_KBN = GENZAIKO.BUT_CENTER_KBN
and BUT.BUT_CENTER = GENZAIKO.BUT_CENTER
-- 前年同月末 --在庫履歴から算出
left join (
select
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
,sum(ZAIKO.TO_ZAN * SHOHIN.PS_SAISU) AS SAISU
from DZ0130 ZAIKO
inner join MS0090 SHOHIN
on ZAIKO.SHIIRE_CD = SHOHIN.SHIIRE_CD
and ZAIKO.SHOHIN_CD = SHOHIN.SHOHIN_CD
where ZAIKO.BUT_CENTER_KBN = '2'
and ZAIKO.YM in (:LAST_YEAR_MONTH_TO)
group by
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
) ZENNEN_ZAIKO
on BUT.BUT_CENTER_KBN = ZENNEN_ZAIKO.BUT_CENTER_KBN
and BUT.BUT_CENTER = ZENNEN_ZAIKO.BUT_CENTER
--前月末 --在庫履歴から算出
left join (
select
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
,sum(ZAIKO.TO_ZAN * SHOHIN.PS_SAISU) AS SAISU
from DZ0130 ZAIKO
inner join MS0090 SHOHIN
on ZAIKO.SHIIRE_CD = SHOHIN.SHIIRE_CD
and ZAIKO.SHOHIN_CD = SHOHIN.SHOHIN_CD
where ZAIKO.BUT_CENTER_KBN = '2'
and ZAIKO.YM in (:LAST_MONTH_TO)
group by
ZAIKO.BUT_CENTER_KBN
,ZAIKO.BUT_CENTER
) ZENGETU_ZAIKO
on BUT.BUT_CENTER_KBN = ZENGETU_ZAIKO.BUT_CENTER_KBN
and BUT.BUT_CENTER = ZENGETU_ZAIKO.BUT_CENTER
-- 当月入庫 --仕入ベースの算出
left join (
select
DS.BUT_CENTER_KBN
,DS.BUT_CENTER
,SUM(DS.TEISEI_BARA_SU * S.PS_SAISU) SAISU
from DS0020 DS
left join MS0090 S
on S.SHIIRE_CD = DS.SHIIRE_CD
and S.SHOHIN_CD = DS.SHOHIN_CD
where 1=1
and DS.SHIIRE_YMD between :MONTH_FROM and :MONTH_TO
and DS.LAST_DEN_FLG = 1
and DS.BUT_CENTER_KBN = '2'
group by
DS.BUT_CENTER_KBN
,DS.BUT_CENTER
) IN_SAISU
on BUT.BUT_CENTER_KBN = IN_SAISU.BUT_CENTER_KBN
and BUT.BUT_CENTER = IN_SAISU.BUT_CENTER
-- 当月出庫 --売上ベースの算出
left join (
select
U.SHUKKA_BUT_CENTER_KBN
,U.SHUKKA_BUT_CENTER
,SUM(U.BARA_SU * S.PS_SAISU) SAISU
from DU0020 U
left join MS0090 S
on S.SHIIRE_CD = U.SHIIRE_CD
and S.SHOHIN_CD = U.SHOHIN_CD
where 1=1
and U.URI_YMD between :MONTH_FROM and :MONTH_TO
and U.LAST_DEN_FLG = '1'
and U.URI_HEN_KBN in ('12')
and U.SHUKKA_BUT_CENTER_KBN = '2'
group by
U.SHUKKA_BUT_CENTER_KBN
,U.SHUKKA_BUT_CENTER
) OUT_SAISU
on BUT.BUT_CENTER_KBN = OUT_SAISU.SHUKKA_BUT_CENTER_KBN
and BUT.BUT_CENTER = OUT_SAISU.SHUKKA_BUT_CENTER
where BUT.BUT_CENTER_KBN = '2'
order by
BUT.BUT_CENTER