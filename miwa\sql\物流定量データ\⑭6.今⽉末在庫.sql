SELECT
TMP.物流センタ区分
,TMP.物流センタCD
,TMP.在庫区分CD
,TMP.仕入商品CD
,TMP.仕入先CD
,TMP.仕入先名
,TMP.仕入先略称
,TMP.商品CD
,TMP.商品名
,TMP.入数①
,TMP.入数②
,TMP.入数③
,TMP.規格
,TMP.賞味期限日数_夏
,TMP.賞味期限日数_冬
,TMP.賞味在引使用区分
,TMP.サイズ_合わせ奥行
,TMP.サイズ_合わせ幅
,TMP.サイズ_合わせ高さ
,TMP.サイズ_ケース奥行
,TMP.サイズ_ケース幅
,TMP.サイズ_ケース高さ
,TMP.サイズ_ボール奥行
,TMP.サイズ_ボール幅
,TMP.サイズ_ボール高さ
,TMP.サイズ_ピース奥行
,TMP.サイズ_ピース幅
,TMP.サイズ_ピース高さ
,TMP.賞味期限
,TMP.特売得意先KEY
,TMP.特売得意先CD
,TMP.特売内部発注NO
,TMP.発注NO
,TMP.発注行NO
,TMP.在庫数
,TMP.仮引バラ数量
,TMP.在庫引当数_P
,TMP.原価
,TMP.新規輸入品で滞留
,TMP."KEY"
,TMP.入荷日
,TMP.入荷バラ数
,TMP.論理出荷バラ数
,TMP.論理出荷B数
,TMP.今週出荷B数
,TMP.消化率
,TMP.入荷後経過日数
,TMP.P才数
,TMP.才数
,TMP.同一センター同一商品の在庫割合
,TMP.日平均受注
,TMP.在庫日数
,TMP.物流センタ名
,TMP.物流センタ建屋
,TMP.在庫区分名
,TMP."物流センタ・在庫区分毎の割合"
,TMP.帳簿残B換算数
,TMP.在庫数 * TMP.原価 帳簿金額
,TMP.賞味日数
,TMP.出荷許容3
,ROUND( TMP.賞味日数 * TMP.出荷許容3, 0) 出荷許容日数3
,TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容3, 0) 出荷許容日3
,TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容3, 0) - DATEUTILS.MIYA_TO_DATE(:TODAY) 出荷許容残日数3
,CASE WHEN TMP.賞味日数 * TMP.出荷許容3 = 0
THEN 0
ELSE ROUND( (TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容3, 0) - DATEUTILS.MIYA_TO_DATE(:TODAY)) / (TMP.賞味日数 * TMP.出荷許容3) * 100, 2)
END 経過割合3
,NULL 出荷許容残日数在庫日数差3
,TMP.出荷許容2
,ROUND( TMP.賞味日数 * TMP.出荷許容2, 0) 出荷許容日数2
,TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容2, 0) 出荷許容日2
,TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容2, 0) - DATEUTILS.MIYA_TO_DATE(:TODAY) 出荷許容残日数2
,CASE WHEN TMP.賞味日数 * TMP.出荷許容2 = 0
THEN 0
ELSE ROUND( (TMP.賞味期限 - TMP.賞味日数 + ROUND( TMP.賞味日数 * TMP.出荷許容2, 0) - DATEUTILS.MIYA_TO_DATE(:TODAY)) / (TMP.賞味日数 * TMP.出荷許容2) * 100, 2)
END 経過割合2
,NULL 出荷許容残日数在庫日数差2
,TMP.在庫数 * TMP.原価 ソート係数
,TMP.前週消化率
,TMP.特記事項
FROM (
SELECT
DZ30.BUT_CENTER_KBN 物流センタ区分
,DZ30.BUT_CENTER 物流センタCD
,DZ30.ZAIKO_KBN 在庫区分CD
,DZ30.SHI_SHO_CD 仕入商品CD
,DZ30.SHIIRE_CD 仕入先CD
,MS80.SHIIRE_MEI 仕入先名
,MS80.SHIIRE_RYA 仕入先略称
,DZ30.SHOHIN_CD 商品CD
,MS90.SHOHIN_MEI 商品名
,MS90.IRISU1 入数①
,MS90.IRISU2 入数②
,MS90.IRISU3 入数③
,MS90.KIKAKU 規格
,MS90.SHOMI_NISU_NATU 賞味期限日数_夏
,MS90.SHOMI_NISU_FUYU 賞味期限日数_冬
,MS90.SHOMI_ZAIBIKI_SHIYO_KBN 賞味在引使用区分
,MS90.AW_SIZE_OKU サイズ_合わせ奥行
,MS90.AW_SIZE_HABA サイズ_合わせ幅
,MS90.AW_SIZE_TAKA サイズ_合わせ高さ
,MS90.CS_SIZE_OKU サイズ_ケース奥行
,MS90.CS_SIZE_HABA サイズ_ケース幅
,MS90.CS_SIZE_TAKA サイズ_ケース高さ
,MS90.BL_SIZE_OKU サイズ_ボール奥行
,MS90.BL_SIZE_HABA サイズ_ボール幅
,MS90.BL_SIZE_TAKA サイズ_ボール高さ
,MS90.PS_SIZE_OKU サイズ_ピース奥行
,MS90.PS_SIZE_HABA サイズ_ピース幅
,MS90.PS_SIZE_TAKA サイズ_ピース高さ
,DATEUTILS.MIYA_TO_DATE(DZ30.SHOMI_KIGEN_YMD) 賞味期限
,DZ30.TOKUI1_KEY 特売得意先KEY
,DZ30.TOKUI1_CD 特売得意先CD
,DZ30.NAI_HAC_DEN_NO 特売内部発注NO
,DZ30.HAC_DEN_NO 発注NO
,DZ30.HAC_LINE_NO 発注行NO
,DZ30.ZAIKO_SU 在庫数
,DZ30.KARIBIKI_BARA_SU 仮引バラ数量
,DZ30.ZAIKO_HIKIATE_SU 在庫引当数_P
,NVL(MS11.GENKA, 0)
+NVL(MS11.SONOTA_GENKA, 0)
+NVL(MS11.SONOTA_GENKA2, 0)
+NVL(MS11.KAKOHI, 0)
+NVL(MS11.UNCHIN, 0)
+NVL(MS11.HANSOKUHI, 0) 原価
,NULL 新規輸入品で滞留
,NULL "KEY"
,MS290.BUT_CENTER_RYA 物流センタ名
,NULL 物流センタ建屋
,MS20.NAME 在庫区分名
,NULL "物流センタ・在庫区分毎の割合"
,ROUND( DZ30.ZAIKO_SU / MS90.IRISU1 , 1) 帳簿残B換算数
,CASE MS90.SHOMI_ZAIBIKI_SHIYO_KBN
WHEN '1' THEN MS90.SHOMI_NISU_NATU
WHEN '2' THEN MS90.SHOMI_NISU_FUYU
ELSE MS90.SHOMI_NISU_FUYU
END 賞味日数
,ROUND(1/3, 4) 出荷許容3
,ROUND(1/2, 4) 出荷許容2
,NULL 前週消化率
,NULL 特記事項
,ROUND((MS90.PS_SIZE_OKU/1000 * MS90.PS_SIZE_HABA/1000 * MS90.PS_SIZE_TAKA/1000), 6) P才数
,ROUND((MS90.PS_SIZE_OKU/1000 * MS90.PS_SIZE_HABA/1000 * MS90.PS_SIZE_TAKA/1000) * DZ30.ZAIKO_SU, 3) 才数
,ROUND(RATIO_TO_REPORT(DZ30.ZAIKO_SU) OVER (PARTITION BY DZ30.BUT_CENTER_KBN, DZ30.BUT_CENTER, DZ30.SHI_SHO_CD) * 100, 2) 同一センター同一商品の在庫割合
,NULL 入荷日
,NULL 入荷バラ数
,NULL 論理出荷バラ数
,NULL 論理出荷B数
,NULL 消化率
,NULL 入荷後経過日数
,NULL 日平均受注
,NULL 在庫日数
,NULL 今週出荷B数
FROM
DZ0030 DZ30
LEFT JOIN MS0080 MS80
ON DZ30.SHIIRE_CD = MS80.SHIIRE_CD
LEFT JOIN MS0090 MS90
ON DZ30.SHIIRE_CD = MS90.SHIIRE_CD
AND DZ30.SHOHIN_CD = MS90.SHOHIN_CD
LEFT JOIN MS0110 MS11
ON DZ30.SHIIRE_CD = MS11.SHIIRE_CD
AND DZ30.SHOHIN_CD = MS11.SHOHIN_CD
AND DZ30.BUT_CENTER_KBN = MS11.BUT_CENTER_KBN
AND DZ30.BUT_CENTER = MS11.JIG_LIN_CD
LEFT JOIN MS0290 MS290
ON MS290.BUT_CENTER_KBN = DZ30.BUT_CENTER_KBN
AND MS290.BUT_CENTER = DZ30.BUT_CENTER
LEFT JOIN MS0020 MS20
ON MS20.SHIKIBETU_CD = 'ZAIKKB'
AND MS20.REC_KBN = '2'
AND MS20.CD = DZ30.ZAIKO_KBN
WHERE 1=1
AND DZ30.ZAIKO_SU > 0
AND MS11.JIG_LIN_KBN = '1'
AND MS11.TEKIYO_KIKAN_TO = '99999999'
) TMP
WHERE 1=1
ORDER BY
ソート係数 DESC NULLS LAST