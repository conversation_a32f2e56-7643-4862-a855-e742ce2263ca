"""
Excel ファイル操作ユーティリティ
"""

import logging
from pathlib import Path
from typing import Dict, Optional, Union

import pandas as pd  # type: ignore
from openpyxl import load_workbook  # type: ignore
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side  # type: ignore

logger = logging.getLogger(__name__)


class ExcelWriter:
    """Excel ファイルへの書き込みを管理するクラス"""

    @staticmethod
    def write_to_new_excel(
        df: pd.DataFrame,
        output_path: Union[str, Path],
        sheet_name: str = "データ",
        index: bool = False,
        freeze_panes: Optional[str] = "A2",
    ) -> None:
        """新規Excelファイルにデータを書き込む

        Args:
            df: 書き込むDataFrame
            output_path: 出力ファイルパス
            sheet_name: シート名
            index: インデックスを含めるかどうか
            freeze_panes: 固定するセル位置（例: "A2"でヘッダー行を固定）
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=index)

                # 固定ペインの設定
                if freeze_panes:
                    worksheet = writer.sheets[sheet_name]
                    worksheet.freeze_panes = freeze_panes

            logger.info(f"Excelファイルを作成しました: {output_path}")

        except Exception as e:
            logger.error(f"Excel書き込みエラー: {e}")
            raise

    @staticmethod
    def write_to_template(
        df: pd.DataFrame,
        template_path: Union[str, Path],
        output_path: Union[str, Path],
        sheet_name: str,
        start_row: int = 2,
        start_col: int = 1,
        clear_existing: bool = False,
    ) -> None:
        """既存のExcelテンプレートにデータを書き込む

        Args:
            df: 書き込むDataFrame
            template_path: テンプレートファイルパス
            output_path: 出力ファイルパス
            sheet_name: シート名
            start_row: データ開始行（1始まり）
            start_col: データ開始列（1始まり）
            clear_existing: 既存データをクリアするかどうか
        """
        try:
            template_path = Path(template_path)
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # テンプレートを読み込む
            wb = load_workbook(template_path)
            ws = wb[sheet_name]

            # 既存データのクリア（オプション）
            if clear_existing:
                max_row = ws.max_row
                max_col = ws.max_column
                for row in range(start_row, max_row + 1):
                    for col in range(start_col, max_col + 1):
                        ws.cell(row=row, column=col, value=None)

            # DataFrameをExcelに書き込む（ヘッダーなし）
            for r_idx, row in enumerate(df.values):
                for c_idx, value in enumerate(row):  # type: ignore
                    ws.cell(
                        row=start_row + r_idx, column=start_col + c_idx, value=value
                    )

            # 保存
            wb.save(output_path)
            logger.info(f"テンプレートにデータを書き込みました: {output_path}")

        except Exception as e:
            logger.error(f"テンプレート書き込みエラー: {e}")
            raise

    @staticmethod
    def write_multiple_sheets(
        dataframes: Dict[str, pd.DataFrame],
        output_path: Union[str, Path],
        index: bool = False,
    ) -> None:
        """複数のDataFrameを異なるシートに書き込む

        Args:
            dataframes: シート名とDataFrameの辞書
            output_path: 出力ファイルパス
            index: インデックスを含めるかどうか
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
                for sheet_name, df in dataframes.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=index)

            logger.info(f"複数シートのExcelファイルを作成しました: {output_path}")

        except Exception as e:
            logger.error(f"複数シート書き込みエラー: {e}")
            raise

    @staticmethod
    def format_excel(
        file_path: Union[str, Path],
        sheet_name: str,
        header_fill_color: str = "366092",
        header_font_color: str = "FFFFFF",
        auto_fit: bool = True,
    ) -> None:
        """Excelファイルのフォーマットを整える

        Args:
            file_path: Excelファイルパス
            sheet_name: シート名
            header_fill_color: ヘッダー背景色（16進数）
            header_font_color: ヘッダー文字色（16進数）
            auto_fit: 列幅を自動調整するかどうか
        """
        try:
            file_path = Path(file_path)
            wb = load_workbook(file_path)
            ws = wb[sheet_name]

            # ヘッダー行のフォーマット
            header_fill = PatternFill(
                start_color=header_fill_color,
                end_color=header_fill_color,
                fill_type="solid",
            )
            header_font = Font(color=header_font_color, bold=True)

            for cell in ws[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # 罫線の設定
            thin_border = Border(
                left=Side(style="thin"),
                right=Side(style="thin"),
                top=Side(style="thin"),
                bottom=Side(style="thin"),
            )

            for row in ws.iter_rows(
                min_row=1, max_row=ws.max_row, max_col=ws.max_column
            ):
                for cell in row:
                    cell.border = thin_border

            # 列幅の自動調整
            if auto_fit:
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter  # type: ignore

                    for cell in column:
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))

                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

            wb.save(file_path)
            logger.info(f"Excelファイルのフォーマットを整えました: {file_path}")

        except Exception as e:
            logger.error(f"Excelフォーマットエラー: {e}")
            raise


class ExcelReader:
    """Excel ファイルの読み込みを管理するクラス"""

    @staticmethod
    def read_excel(
        file_path: Union[str, Path],
        sheet_name: Union[str, int] = 0,
        header: Optional[int] = 0,
        skiprows: Optional[int] = None,
        usecols: Optional[Union[str, list]] = None,
    ) -> pd.DataFrame:
        """Excelファイルを読み込んでDataFrameを返す

        Args:
            file_path: Excelファイルパス
            sheet_name: シート名またはインデックス
            header: ヘッダー行のインデックス
            skiprows: スキップする行数
            usecols: 読み込む列の指定

        Returns:
            読み込んだDataFrame
        """
        try:
            file_path = Path(file_path)
            df = pd.read_excel(
                file_path,
                sheet_name=sheet_name,
                header=header,
                skiprows=skiprows,
                usecols=usecols,
            )
            logger.info(f"Excelファイルを読み込みました: {file_path} ({len(df)}行)")
            return df

        except Exception as e:
            logger.error(f"Excel読み込みエラー: {e}")
            raise

    @staticmethod
    def read_all_sheets(file_path: Union[str, Path]) -> Dict[str, pd.DataFrame]:
        """Excelファイルの全シートを読み込む

        Args:
            file_path: Excelファイルパス

        Returns:
            シート名とDataFrameの辞書
        """
        try:
            file_path = Path(file_path)
            xl_file = pd.ExcelFile(file_path)
            dataframes = {}

            for sheet_name in xl_file.sheet_names:
                dataframes[sheet_name] = xl_file.parse(sheet_name)
                logger.info(
                    f"シート '{sheet_name}' を読み込みました ({len(dataframes[sheet_name])}行)"
                )

            return dataframes

        except Exception as e:
            logger.error(f"Excel全シート読み込みエラー: {e}")
            raise
