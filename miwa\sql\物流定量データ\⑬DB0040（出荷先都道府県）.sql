SELECT
DB40.<PERSON><PERSON><PERSON><PERSON><PERSON>YM
,DB40.<PERSON><PERSON><PERSON><PERSON>_KBN
,DB40.SAGYO_KEITAI
,DB40.SAGYO_KEITAI2
,DB40.MOTO_BUT_CENTER_KBN AS 物流センタ区分
,DB40.MOTO_BUT_CENTER_CD AS 物流センタCD
,MS29.BUT_CENTER_RYA AS 物流センタ略称
,SUBSTR(DB40.SAKI_JYUSHO,0,INSTR(SUBSTR(REPLACE(REPLACE(REPLACE(REPLACE(DB40.SAKI_JYUSHO,'都','都|'),'道','道|'),'府','府|'),'県','県|'),0,10),'|')-1) AS 都道府県
,SUM(DB40.KAKU_UNCHIN)+SUM(DB40.KAKU_UNCHIN_ZIPPI) 運賃
,SUM(DB40.SAISU) AS 才数
,ROUND(AVG(DB40.C_KYORITEI),-1) AS 平均距離程
FROM
DB0040 DB40
LEFT JOIN MS0290 MS29
ON DB40.MOTO_BUT_CENTER_KBN = MS29.BUT_CENTER_KBN
AND DB40.MOTO_BUT_CENTER_CD = MS29.BUT_CENTER
WHERE
DB40.KEIJO_YM = :TARGET_YM OR DB40.KEIJO_YM = :LAST_YEAR_YM
GROUP BY
DB40.KEIJO_YM
,DB40.KEIJO_KBN
,DB40.SAGYO_KEITAI
,DB40.SAGYO_KEITAI2
,DB40.MOTO_BUT_CENTER_KBN
,DB40.MOTO_BUT_CENTER_CD
,MS29.BUT_CENTER_RYA
,SUBSTR(DB40.SAKI_JYUSHO,0,INSTR(SUBSTR(REPLACE(REPLACE(REPLACE(REPLACE(DB40.SAKI_JYUSHO,'都','都|'),'道','道|'),'府','府|'),'県','県|'),0,10),'|')-1)
ORDER BY
DB40.KEIJO_YM
,DB40.KEIJO_KBN
,DB40.SAGYO_KEITAI
,DB40.SAGYO_KEITAI2
,DB40.MOTO_BUT_CENTER_KBN
,DB40.MOTO_BUT_CENTER_CD
,MS29.BUT_CENTER_RYA
,SUM(DB40.SAISU) DESC