select
CAL.CD as 識別CD
,CAL.NAME as 対象期間
,NVL( SUM(DB40.KAKU_UNCHIN + DB40.KAKU_UNCHIN_ZIPPI), 0) as 運搬梱包費
from
(
select 1 as CD, '今週' as NAME, :WEEK_FROM as FROMDATE, :WEEK_TO as TODATE from dual union all
select 2 as CD, '前年同週' as NAME, :LAST_YEAR_WEEK_FROM as FROMDATE, :LAST_YEAR_WEEK_TO as TODATE from dual union all
select 3 as CD, '前週' as NAME, :LAST_WEEK_FROM as FROMDATE, :LAST_WEEK_TO as TODATE from dual union all
select 4 as CD, '当月累積' as NAME, :MONTH_FROM as FROMDATE, :MONTH_TO as TODATE from dual union all
select 5 as CD, '前年同月' as NAME, :LAST_YEAR_MONTH_FROM as FROMDATE, :LAST_YEAR_MONTH_TO as TODATE from dual
) <PERSON><PERSON>
left join DB0040 DB40
on DB40.KEIJO_YMD between CAL.FROMDATE and CAL.TODATE
group by
CD
,NAME
order by
CD
,NAME