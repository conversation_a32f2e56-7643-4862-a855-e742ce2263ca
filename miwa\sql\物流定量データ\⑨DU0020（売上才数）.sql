SELECT
SUBSTR(DU20.URI_YMD,0,6) URI_YM
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
-- ,DU20.SHIIRE_CD
-- ,DU20.SHOHIN_CD
-- ,SUM(DU20.BARA_SU)
-- ,DU20.TANI
,TRUNC( SUM(
CASE
WHEN DU20.TANI = 'A'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000)) * DU20.BARA_SU
WHEN DU20.TANI = 'C'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000)) * DU20.BARA_SU
WHEN DU20.TANI = 'B'
THEN ((MS90.BL_SIZE_OKU / 1000) * (MS90.BL_SIZE_HABA / 1000) * (MS90.BL_SIZE_TAKA / 1000)) * DU20.BARA_SU
WHEN DU20.TANI = 'P'
THEN ((MS90.PS_SIZE_OKU / 1000) * (MS90.PS_SIZE_HABA / 1000) * (MS90.PS_SIZE_TAKA / 1000)) * DU20.BARA_SU
ELSE 0
END
), 2) AS 才数
FROM
DU0020 DU20
LEFT JOIN MS0090 MS90
ON MS90.SHIIRE_CD = DU20.SHIIRE_CD
AND MS90.SHOHIN_CD = DU20.SHOHIN_CD
WHERE
(DU20.URI_YMD LIKE :TARGET_YM ||'%' OR DU20.URI_YMD LIKE :LAST_YEAR_YM ||'%')
GROUP BY
SUBSTR(DU20.URI_YMD,0,6)
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
-- ,DU20.SHIIRE_CD
-- ,DU20.SHOHIN_CD
-- ,DU20.TANI
ORDER BY
SUBSTR(DU20.URI_YMD,0,6)
,DU20.SHUKKA_BUT_CENTER_KBN
,DU20.SHUKKA_BUT_CENTER
-- ,DU20.SHIIRE_CD
-- ,DU20.SHOHIN_CD
-- ,DU20.TANI