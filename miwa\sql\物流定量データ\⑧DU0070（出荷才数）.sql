SELECT
SUBSTR(DU70.YMD_SHP,0,6) YM_SHP
,DU70.CD_CNT
-- ,DU70.CD_ITEM
-- ,SUM(DU70.NUM_SHPPLN)
-- ,DU70.CLS_ORD_UNIT
,TRUNC( SUM(
CASE
WHEN DU70.CLS_ORD_UNIT = 'A'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DU70.NUM_SHPPLN / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DU70.CLS_ORD_UNIT = 'C'
THEN ((MS90.CS_SIZE_OKU / 1000) * (MS90.CS_SIZE_HABA / 1000) * (MS90.CS_SIZE_TAKA / 1000) * (DU70.NUM_SHPPLN / (MS90.IRISU1 * MS90.IRISU2) ) / 0.0278)
WHEN DU70.CLS_ORD_UNIT = 'B'
THEN ((MS90.BL_SIZE_OKU / 1000) * (MS90.BL_SIZE_HABA / 1000) * (MS90.BL_SIZE_TAKA / 1000) * (DU70.NUM_SHPPLN / (MS90.IRISU1) ) / 0.0278)
WHEN DU70.CLS_ORD_UNIT = 'P'
THEN ((MS90.PS_SIZE_OKU / 1000) * (MS90.PS_SIZE_HABA / 1000) * (MS90.PS_SIZE_TAKA / 1000) * (DU70.NUM_SHPPLN) / 0.0278)
ELSE 0
END
), 2) AS 才数
-- ,MS90.SHI_SHO_CD
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
FROM
DU0070 DU70
LEFT JOIN MS0090 MS90
ON MS90.SHI_SHO_CD = DU70.CD_ITEM
LEFT JOIN MS0020 MS20_SIHABU
ON MS20_SIHABU.SHIKIBETU_CD = 'SIHABU'
AND MS20_SIHABU.REC_KBN = '2'
AND MS20_SIHABU.CD = MS90.KAMOKU_BUNRUI
LEFT JOIN MS0020 MS20_BUMBUN
ON MS20_BUMBUN.SHIKIBETU_CD = 'BUMBUN'
AND MS20_BUMBUN.REC_KBN = '2'
AND MS20_BUMBUN.CD = MS90.BUMON_BUNRUI
WHERE
(DU70.YMD_SHP LIKE :TARGET_YM ||'%' OR DU70.YMD_SHP LIKE :LAST_YEAR_YM ||'%' OR DU70.YMD_SHP LIKE :LAST_MONTH_YM ||'%' OR DU70.YMD_SHP LIKE :LAST_BOTH_YM ||'%')
-- AND MS90.SHUBAI_YMD IS NULL
AND MS90.KAMOKU_BUNRUI IN ('10','20','30')
GROUP BY
SUBSTR(DU70.YMD_SHP,0,6)
,DU70.CD_CNT
-- ,MS90.SHI_SHO_CD
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
-- ,DU70.CD_ITEM
-- ,DU70.CLS_ORD_UNIT
ORDER BY
SUBSTR(DU70.YMD_SHP,0,6)
,DU70.CD_CNT
-- ,MS90.SHI_SHO_CD
,MS90.KAMOKU_BUNRUI
,MS20_SIHABU.NAME
,MS90.BUMON_BUNRUI
,MS20_BUMBUN.NAME
-- ,DU70.CD_ITEM
-- ,DU70.CLS_ORD_UNIT