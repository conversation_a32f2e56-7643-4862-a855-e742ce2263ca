-- DZ0030 在庫データのスナップ作成
delete from DZ0030_SNAP where SNAP_YMD = (select SHORI_YMD from MS0010)
;
insert into DZ0030_SNAP
(
SNAP_YMD ,SNAP_YM ,BUT_CENTER_KBN ,BUT_CENTER ,ZAIKO_KBN
,SHIIRE_CD ,SHOHIN_CD ,SHI_SHO_CD ,SHOMI_KIGEN_YMD ,TOKUI1_KEY
,TOKUI1_CD ,NAI_HAC_DEN_NO ,HAC_DEN_NO ,HAC_LINE_NO ,ZAIKO_SU
,KARIBIKI_BARA_SU ,ZAIKO_HIKIATE_SU
,TOROKU_YMD ,TOROKU_TIME ,TOROKU_CD ,TOROKU_PG
,KOSHIN_YMD ,KOSHIN_TIME ,KOSHIN_CD ,KOSHIN_PG
,KOSHIN_KAISU
)
select
A.SHORI_YMD ,<PERSON>.GETUJI_YM ,B.BUT_CENTER_KBN ,<PERSON><PERSON><PERSON>UT_CENTER ,<PERSON><PERSON><PERSON>
,<PERSON><PERSON>SHIIRE_CD ,B<PERSON>SHOHIN_CD ,B<PERSON>SHI_SHO_CD ,B.SHOMI_KIGEN_YMD ,B.T
,B.TOKUI1_CD ,B.NAI_HAC_DEN_NO ,B.HAC_DEN_NO ,B.HAC_LINE_NO ,B.Z
,B.KARIBIKI_BARA_SU ,B.ZAIKO_HIKIATE_SU
,B.TOROKU_YMD ,B.TOROKU_TIME ,B.TOROKU_CD ,B.TOROKU_PG
,B.KOSHIN_YMD ,B.KOSHIN_TIME ,B.KOSHIN_CD ,B.KOSHIN_PG
,B.KOSHIN_KAISU
from
MS0010 A
,DZ0030 B
;
commit
;
exit
;