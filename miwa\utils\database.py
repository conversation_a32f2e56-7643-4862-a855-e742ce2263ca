"""
Oracle データベース接続ユーティリティ
"""

import logging
import os
from contextlib import contextmanager

import oracledb
import pandas as pd  # type: ignore

logger = logging.getLogger(__name__)


class OracleDatabase:
    """Oracle データベース接続を管理するクラス"""

    def __init__(self):
        self.connection = None

    def connect(self):
        """データベースに接続"""
        try:
            # 接続情報を環境変数から取得
            dsn = oracledb.makedsn(
                os.getenv("ORACLE_HOST", "localhost"),
                int(os.getenv("ORACLE_PORT", "1521")),
                service_name=os.getenv("ORACLE_SERVICE_NAME", "ORCL"),
            )

            self.connection = oracledb.connect(
                user=os.getenv("ORACLE_USER"),
                password=os.getenv("ORACLE_PASSWORD"),
                dsn=dsn,
            )
            logger.info(f"Oracle データベースに接続しました: {dsn}")
            return self.connection

        except Exception as e:
            logger.error(f"接続エラー: {e}")
            raise

    def disconnect(self):
        """データベースから切断"""
        if self.connection:
            self.connection.close()
            logger.info("Oracle データベースから切断しました")

    def execute_query(self, sql, params=None):
        """SQLクエリを実行してDataFrameを返す"""
        if not self.connection:
            raise RuntimeError(
                "データベースに接続されていません。connect()を先に呼び出してください。"
            )

        try:
            # pandasでSQLを実行
            if params:
                df = pd.read_sql(sql, self.connection, params=params)
            else:
                df = pd.read_sql(sql, self.connection)

            logger.info(f"取得件数: {len(df)}件")
            return df

        except Exception as e:
            logger.error(f"クエリ実行エラー: {e}")
            raise

    def execute_update(self, sql, params=None):
        """INSERT/UPDATE/DELETE クエリを実行"""
        if not self.connection:
            raise RuntimeError(
                "データベースに接続されていません。connect()を先に呼び出してください。"
            )

        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            row_count = cursor.rowcount
            self.connection.commit()
            cursor.close()

            logger.info(f"更新件数: {row_count}件")
            return row_count

        except Exception as e:
            logger.error(f"更新クエリ実行エラー: {e}")
            if self.connection:
                self.connection.rollback()
            raise

    def execute_many(self, sql, params_list):
        """複数行の INSERT/UPDATE を実行"""
        if not self.connection:
            raise RuntimeError(
                "データベースに接続されていません。connect()を先に呼び出してください。"
            )

        try:
            cursor = self.connection.cursor()
            cursor.executemany(sql, params_list)

            row_count = cursor.rowcount
            self.connection.commit()
            cursor.close()

            logger.info(f"更新件数: {row_count}件")
            return row_count

        except Exception as e:
            logger.error(f"バッチ更新エラー: {e}")
            if self.connection:
                self.connection.rollback()
            raise

    @contextmanager
    def transaction(self):
        """トランザクション管理のコンテキストマネージャー"""
        if not self.connection:
            raise RuntimeError(
                "データベースに接続されていません。connect()を先に呼び出してください。"
            )
        try:
            yield self
            self.connection.commit()
            logger.info("トランザクションをコミットしました")
        except Exception as e:
            self.connection.rollback()
            logger.error(f"トランザクションをロールバックしました: {e}")
            raise


@contextmanager
def get_db_connection():
    """データベース接続のコンテキストマネージャー"""
    db = OracleDatabase()
    try:
        db.connect()
        yield db
    finally:
        db.disconnect()


def read_sql_file(file_path, encoding="utf-8"):
    """SQLファイルを読み込む"""
    try:
        with open(file_path, "r", encoding=encoding) as f:
            sql = f.read()
        logger.info(f"SQLファイルを読み込みました: {file_path}")
        return sql
    except Exception as e:
        logger.error(f"SQLファイル読み込みエラー: {e}")
        raise
